"""
Configuration module for the Knowledge Base Bot.

This module defines all configuration classes and settings required for
the knowledge base bot components including ingestion and retrieval settings.
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from enum import Enum
import os


class ActivityType(str, Enum):
    """Enumeration of cattle activity types for filtering."""
    BULL_CALF = "bull_calf"
    COWS = "cows"
    GENERAL = "general"


class ParsingConfig(BaseModel):
    """Configuration for document parsing using Docling."""
    
    # Docling specific settings
    enable_ocr: bool = Field(default=True, description="Enable OCR for image-based content")
    extract_tables: bool = Field(default=True, description="Extract tables from documents")
    extract_images: bool = Field(default=True, description="Extract images from documents")
    
    # File type support
    supported_formats: List[str] = Field(
        default=["pdf", "docx", "pptx", "html", "md", "txt"],
        description="Supported document formats"
    )
    
    # Processing settings
    max_file_size_mb: int = Field(default=100, description="Maximum file size in MB")
    timeout_seconds: int = Field(default=300, description="Parsing timeout in seconds")


class ChunkingConfig(BaseModel):
    """Configuration for document chunking and filtering."""
    
    # Chunking strategy
    chunk_size: int = Field(default=1000, description="Target chunk size in characters")
    chunk_overlap: int = Field(default=200, description="Overlap between chunks in characters")
    
    # Activity-based filtering
    enable_activity_filtering: bool = Field(default=True, description="Enable activity-based filtering")
    activity_keywords: Dict[ActivityType, List[str]] = Field(
        default={
            ActivityType.BULL_CALF: ["bull", "calf", "young cattle", "breeding"],
            ActivityType.COWS: ["cow", "dairy", "milking", "lactation", "herd"],
            ActivityType.GENERAL: ["cattle", "livestock", "farm", "agriculture"]
        },
        description="Keywords for activity classification"
    )
    
    # Chunk quality settings
    min_chunk_length: int = Field(default=100, description="Minimum chunk length in characters")
    max_chunk_length: int = Field(default=2000, description="Maximum chunk length in characters")


class VectorizationConfig(BaseModel):
    """Configuration for text vectorization using OpenAI models."""
    
    # OpenAI settings
    api_key: str = Field(description="OpenAI API key")
    model_name: str = Field(default="text-embedding-3-small", description="OpenAI embedding model")
    embedding_dimension: int = Field(default=1536, description="Embedding vector dimension")
    
    # Processing settings
    batch_size: int = Field(default=100, description="Batch size for vectorization")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    retry_delay: float = Field(default=1.0, description="Delay between retries in seconds")
    
    # Rate limiting
    requests_per_minute: int = Field(default=3000, description="API requests per minute limit")


class VectorStoreConfig(BaseModel):
    """Configuration for Qdrant vector database."""
    
    # Connection settings
    host: str = Field(default="localhost", description="Qdrant host")
    port: int = Field(default=6333, description="Qdrant port")
    api_key: Optional[str] = Field(default=None, description="Qdrant API key for cloud")
    
    # Collection settings
    collection_name: str = Field(default="cattle_knowledge", description="Qdrant collection name")
    vector_size: int = Field(default=1536, description="Vector dimension size")
    distance_metric: str = Field(default="Cosine", description="Distance metric for similarity")
    
    # Performance settings
    shard_number: int = Field(default=1, description="Number of shards")
    replication_factor: int = Field(default=1, description="Replication factor")
    
    # Indexing settings
    m: int = Field(default=16, description="HNSW parameter m")
    ef_construct: int = Field(default=100, description="HNSW parameter ef_construct")


class QueryProcessingConfig(BaseModel):
    """Configuration for query processing and enhancement."""
    
    # Query enhancement
    enable_query_expansion: bool = Field(default=True, description="Enable query expansion")
    enable_query_rephrasing: bool = Field(default=True, description="Enable query rephrasing")
    
    # OpenAI settings for query processing
    api_key: str = Field(description="OpenAI API key")
    model_name: str = Field(default="gpt-4o-mini", description="Model for query processing")
    
    # Activity filter generation
    enable_filter_generation: bool = Field(default=True, description="Generate activity filters from query")
    
    # Processing settings
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    timeout_seconds: int = Field(default=30, description="Query processing timeout")


class RetrievalConfig(BaseModel):
    """Configuration for document retrieval from vector store."""
    
    # Search settings
    top_k: int = Field(default=10, description="Number of top results to retrieve")
    score_threshold: float = Field(default=0.7, description="Minimum similarity score threshold")
    
    # Filtering settings
    enable_activity_filtering: bool = Field(default=True, description="Apply activity-based filtering")
    filter_boost: float = Field(default=0.1, description="Score boost for matching activity filters")
    
    # Reranking settings
    enable_reranking: bool = Field(default=True, description="Enable result reranking")
    rerank_top_k: int = Field(default=20, description="Number of results to consider for reranking")


class ResponseGenerationConfig(BaseModel):
    """Configuration for response generation."""
    
    # OpenAI settings
    api_key: str = Field(description="OpenAI API key")
    model_name: str = Field(default="gpt-4o", description="Model for response generation")
    
    # Generation settings
    max_tokens: int = Field(default=1000, description="Maximum response tokens")
    temperature: float = Field(default=0.1, description="Response generation temperature")
    
    # Context settings
    max_context_chunks: int = Field(default=5, description="Maximum chunks to include in context")
    max_conversation_history: int = Field(default=10, description="Maximum conversation history messages")
    
    # System prompt settings
    system_prompt_template: str = Field(
        default="""You are a knowledgeable cattle farming assistant. Use the provided context to answer questions about cattle management, breeding, health, and farming practices. 

Context: {context}

Previous conversation: {conversation_history}

Instructions:
- Provide accurate, helpful information based on the context
- If the context doesn't contain relevant information, say so clearly
- Focus on practical, actionable advice
- Consider the specific activity type (bull calf, cows, general) when relevant
- Be concise but comprehensive in your responses""",
        description="System prompt template for response generation"
    )
    
    # Processing settings
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    timeout_seconds: int = Field(default=60, description="Response generation timeout")


class KnowledgeBaseConfig(BaseModel):
    """Main configuration class for the Knowledge Base Bot."""
    
    # Component configurations
    parsing: ParsingConfig = Field(default_factory=ParsingConfig)
    chunking: ChunkingConfig = Field(default_factory=ChunkingConfig)
    vectorization: VectorizationConfig
    vector_store: VectorStoreConfig = Field(default_factory=VectorStoreConfig)
    query_processing: QueryProcessingConfig
    retrieval: RetrievalConfig = Field(default_factory=RetrievalConfig)
    response_generation: ResponseGenerationConfig
    
    # Global settings
    log_level: str = Field(default="INFO", description="Logging level")
    enable_metrics: bool = Field(default=True, description="Enable performance metrics")
    
    @classmethod
    def from_env(cls) -> "KnowledgeBaseConfig":
        """
        Create configuration from environment variables.
        
        Returns:
            KnowledgeBaseConfig: Configuration instance
        """
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        qdrant_host = os.getenv("QDRANT_HOST", "localhost")
        qdrant_port = int(os.getenv("QDRANT_PORT", "6333"))
        qdrant_api_key = os.getenv("QDRANT_API_KEY")
        
        return cls(
            vectorization=VectorizationConfig(api_key=openai_api_key),
            vector_store=VectorStoreConfig(
                host=qdrant_host,
                port=qdrant_port,
                api_key=qdrant_api_key
            ),
            query_processing=QueryProcessingConfig(api_key=openai_api_key),
            response_generation=ResponseGenerationConfig(api_key=openai_api_key)
        )
    
    def validate_config(self) -> bool:
        """
        Validate the configuration settings.
        
        Returns:
            bool: True if configuration is valid
        """
        try:
            # Validate API keys are present
            if not self.vectorization.api_key:
                raise ValueError("OpenAI API key is required for vectorization")
            
            if not self.query_processing.api_key:
                raise ValueError("OpenAI API key is required for query processing")
            
            if not self.response_generation.api_key:
                raise ValueError("OpenAI API key is required for response generation")
            
            # Validate vector dimensions match
            if self.vectorization.embedding_dimension != self.vector_store.vector_size:
                raise ValueError("Vectorization embedding dimension must match vector store vector size")
            
            # Validate chunk settings
            if self.chunking.min_chunk_length >= self.chunking.max_chunk_length:
                raise ValueError("Minimum chunk length must be less than maximum chunk length")
            
            return True
            
        except Exception as e:
            raise ValueError(f"Configuration validation failed: {str(e)}")
