"""
Document Chunker with activity-based filtering for cattle farming content.

This module handles intelligent chunking of parsed documents with filtering
based on cattle activities (bull calf, cows, general farming) to improve
retrieval relevance and organization.
"""

from typing import List, Dict, Any, Optional, Set
import re
import asyncio
from dataclasses import dataclass
from loguru import logger

import semchunk
from ..config import ChunkingConfig, ActivityType
from .document_parser import ParsedDocument


@dataclass
class DocumentChunk:
    """
    Container for a document chunk with metadata and activity classification.
    """
    content: str
    metadata: Dict[str, Any]
    activity_types: Set[ActivityType]
    chunk_index: int
    start_char: int
    end_char: int
    word_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chunk to dictionary format."""
        return {
            "content": self.content,
            "metadata": self.metadata,
            "activity_types": list(self.activity_types),
            "chunk_index": self.chunk_index,
            "start_char": self.start_char,
            "end_char": self.end_char,
            "word_count": self.word_count
        }


class DocumentChunker:
    """
    Document chunker with intelligent activity-based filtering.
    
    Chunks documents into manageable pieces while maintaining semantic coherence
    and classifying chunks by cattle farming activities for improved retrieval.
    """
    
    def __init__(self, config: ChunkingConfig):
        """
        Initialize the document chunker.
        
        Args:
            config: Chunking configuration
        """
        self.config = config
        self._compile_activity_patterns()
        logger.info("Document chunker initialized")
    
    def _compile_activity_patterns(self):
        """Compile regex patterns for activity detection."""
        self.activity_patterns = {}
        
        for activity_type, keywords in self.config.activity_keywords.items():
            # Create case-insensitive pattern for each activity
            pattern = r'\b(?:' + '|'.join(re.escape(keyword) for keyword in keywords) + r')\b'
            self.activity_patterns[activity_type] = re.compile(pattern, re.IGNORECASE)
    
    async def chunk(self, parsed_document: ParsedDocument, additional_metadata: Optional[Dict[str, Any]] = None) -> List[DocumentChunk]:
        """
        Chunk a parsed document into smaller pieces with activity classification.
        
        Args:
            parsed_document: The parsed document to chunk
            additional_metadata: Optional additional metadata to include
            
        Returns:
            List of DocumentChunk objects
        """
        try:
            if not parsed_document or not parsed_document.content:
                logger.warning("Empty or invalid document provided for chunking")
                return []
            
            logger.info(f"Chunking document: {parsed_document.metadata.get('file_name', 'unknown')}")
            
            # Prepare base metadata
            base_metadata = parsed_document.metadata.copy()
            if additional_metadata:
                base_metadata.update(additional_metadata)
            
            # Create initial chunks using semantic chunking
            initial_chunks = await self._create_semantic_chunks(parsed_document.content)
            
            # Process each chunk
            document_chunks = []
            for i, chunk_text in enumerate(initial_chunks):
                # Validate chunk length
                if len(chunk_text) < self.config.min_chunk_length:
                    logger.debug(f"Skipping chunk {i}: too short ({len(chunk_text)} chars)")
                    continue
                
                if len(chunk_text) > self.config.max_chunk_length:
                    # Split oversized chunks
                    sub_chunks = await self._split_oversized_chunk(chunk_text)
                    for j, sub_chunk in enumerate(sub_chunks):
                        chunk = await self._create_chunk(
                            sub_chunk, base_metadata, len(document_chunks), parsed_document.content
                        )
                        if chunk:
                            document_chunks.append(chunk)
                else:
                    # Process normal-sized chunk
                    chunk = await self._create_chunk(
                        chunk_text, base_metadata, i, parsed_document.content
                    )
                    if chunk:
                        document_chunks.append(chunk)
            
            # Process tables as separate chunks if present
            if parsed_document.tables:
                table_chunks = await self._process_tables(parsed_document.tables, base_metadata, len(document_chunks))
                document_chunks.extend(table_chunks)
            
            logger.info(f"Created {len(document_chunks)} chunks from document")
            return document_chunks
            
        except Exception as e:
            logger.error(f"Error chunking document: {str(e)}")
            return []
    
    async def _create_semantic_chunks(self, content: str) -> List[str]:
        """
        Create semantic chunks using sentence-based chunking.
        
        Args:
            content: Document content to chunk
            
        Returns:
            List of chunk texts
        """
        try:
            # Use semchunk for intelligent semantic chunking
            # Create a simple token counter (word-based for simplicity)
            def token_counter(text: str) -> int:
                return len(text.split())

            # Use semchunk.chunk for semantic chunking
            chunks = semchunk.chunk(
                content,
                chunk_size=self.config.chunk_size // 4,  # Approximate words to chars ratio
                token_counter=token_counter,
                overlap=self.config.chunk_overlap // 4 if self.config.chunk_overlap > 0 else None
            )
            return chunks

        except Exception as e:
            logger.error(f"Error in semantic chunking: {str(e)}")
            # Fallback to simple text splitting
            return self._simple_chunk_split(content)
    
    def _simple_chunk_split(self, content: str) -> List[str]:
        """
        Fallback simple chunking method.
        
        Args:
            content: Content to chunk
            
        Returns:
            List of chunks
        """
        chunks = []
        start = 0
        
        while start < len(content):
            end = start + self.config.chunk_size
            
            # Try to break at sentence boundary
            if end < len(content):
                # Look for sentence endings within overlap range
                search_start = max(start, end - self.config.chunk_overlap)
                sentence_end = content.rfind('.', search_start, end)
                if sentence_end > start:
                    end = sentence_end + 1
            
            chunk = content[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - self.config.chunk_overlap
        
        return chunks
    
    async def _split_oversized_chunk(self, chunk_text: str) -> List[str]:
        """
        Split chunks that exceed maximum length.
        
        Args:
            chunk_text: Text to split
            
        Returns:
            List of smaller chunks
        """
        sub_chunks = []
        start = 0
        
        while start < len(chunk_text):
            end = start + self.config.max_chunk_length
            
            if end >= len(chunk_text):
                sub_chunks.append(chunk_text[start:].strip())
                break
            
            # Find good break point (sentence or paragraph)
            break_point = chunk_text.rfind('.', start, end)
            if break_point == -1:
                break_point = chunk_text.rfind('\n', start, end)
            if break_point == -1:
                break_point = chunk_text.rfind(' ', start, end)
            
            if break_point > start:
                end = break_point + 1
            
            sub_chunk = chunk_text[start:end].strip()
            if sub_chunk:
                sub_chunks.append(sub_chunk)
            
            start = end
        
        return sub_chunks
    
    async def _create_chunk(self, chunk_text: str, base_metadata: Dict[str, Any], chunk_index: int, full_content: str) -> Optional[DocumentChunk]:
        """
        Create a DocumentChunk with activity classification.
        
        Args:
            chunk_text: The chunk content
            base_metadata: Base metadata from document
            chunk_index: Index of this chunk
            full_content: Full document content for position calculation
            
        Returns:
            DocumentChunk or None if invalid
        """
        try:
            # Calculate position in original document
            start_char = full_content.find(chunk_text)
            if start_char == -1:
                start_char = 0  # Fallback if exact match not found
            end_char = start_char + len(chunk_text)
            
            # Classify activities
            activity_types = self._classify_activities(chunk_text)
            
            # Create chunk metadata
            chunk_metadata = base_metadata.copy()
            chunk_metadata.update({
                "chunk_index": chunk_index,
                "chunk_length": len(chunk_text),
                "activity_types": list(activity_types),
                "has_activity_classification": len(activity_types) > 0
            })
            
            return DocumentChunk(
                content=chunk_text,
                metadata=chunk_metadata,
                activity_types=activity_types,
                chunk_index=chunk_index,
                start_char=start_char,
                end_char=end_char,
                word_count=len(chunk_text.split())
            )
            
        except Exception as e:
            logger.error(f"Error creating chunk: {str(e)}")
            return None
    
    def _classify_activities(self, text: str) -> Set[ActivityType]:
        """
        Classify chunk content by cattle farming activities.
        
        Args:
            text: Text content to classify
            
        Returns:
            Set of detected activity types
        """
        if not self.config.enable_activity_filtering:
            return {ActivityType.GENERAL}
        
        detected_activities = set()
        
        for activity_type, pattern in self.activity_patterns.items():
            if pattern.search(text):
                detected_activities.add(activity_type)
        
        # If no specific activities detected, mark as general
        if not detected_activities:
            detected_activities.add(ActivityType.GENERAL)
        
        return detected_activities
    
    async def _process_tables(self, tables: List[Dict[str, Any]], base_metadata: Dict[str, Any], start_index: int) -> List[DocumentChunk]:
        """
        Process extracted tables as separate chunks.
        
        Args:
            tables: List of table data
            base_metadata: Base metadata
            start_index: Starting chunk index
            
        Returns:
            List of table chunks
        """
        table_chunks = []
        
        for i, table in enumerate(tables):
            table_content = table.get('content', '')
            if not table_content or len(table_content) < self.config.min_chunk_length:
                continue
            
            # Create table-specific metadata
            table_metadata = base_metadata.copy()
            table_metadata.update({
                "content_type": "table",
                "table_index": i,
                "bbox": table.get('bbox'),
                "page": table.get('page')
            })
            
            # Classify table activities
            activity_types = self._classify_activities(table_content)
            
            chunk = DocumentChunk(
                content=f"Table {i + 1}:\n{table_content}",
                metadata=table_metadata,
                activity_types=activity_types,
                chunk_index=start_index + i,
                start_char=0,  # Tables don't have position in main text
                end_char=len(table_content),
                word_count=len(table_content.split())
            )
            
            table_chunks.append(chunk)
        
        return table_chunks
    
    async def chunk_multiple(self, parsed_documents: List[ParsedDocument], metadata_list: Optional[List[Dict[str, Any]]] = None) -> Dict[str, List[DocumentChunk]]:
        """
        Chunk multiple documents concurrently.
        
        Args:
            parsed_documents: List of parsed documents
            metadata_list: Optional list of additional metadata
            
        Returns:
            Dict mapping document names to chunk lists
        """
        tasks = []
        
        for i, doc in enumerate(parsed_documents):
            additional_metadata = metadata_list[i] if metadata_list and i < len(metadata_list) else None
            task = self.chunk(doc, additional_metadata)
            doc_name = doc.metadata.get('file_name', f'document_{i}')
            tasks.append((doc_name, task))
        
        results = {}
        for doc_name, task in tasks:
            try:
                chunks = await task
                results[doc_name] = chunks
            except Exception as e:
                logger.error(f"Error chunking document {doc_name}: {str(e)}")
                results[doc_name] = []
        
        return results
    
    def get_activity_stats(self, chunks: List[DocumentChunk]) -> Dict[str, int]:
        """
        Get statistics about activity classification in chunks.
        
        Args:
            chunks: List of document chunks
            
        Returns:
            Dict with activity type counts
        """
        stats = {activity.value: 0 for activity in ActivityType}
        
        for chunk in chunks:
            for activity in chunk.activity_types:
                stats[activity.value] += 1
        
        return stats
