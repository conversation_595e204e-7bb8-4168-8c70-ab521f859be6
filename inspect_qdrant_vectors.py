#!/usr/bin/env python3
"""
Script to inspect stored vectors in Qdrant Cloud database.

This script connects to your Qdrant Cloud instance and shows:
- Collection information
- Stored points/vectors
- Metadata and payloads
"""

import asyncio
import sys
from pathlib import Path
from loguru import logger
import json

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from qdrant_client import AsyncQdrantClient


async def inspect_qdrant_vectors():
    """Inspect the vectors stored in Qdrant Cloud."""
    try:
        # Initialize Qdrant client with your production credentials
        client = AsyncQdrantClient(
            url="https://efd8b5e5-78f4-4d71-8510-14d7d593c77f.us-west-1-0.aws.cloud.qdrant.io",
            api_key="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.AoMir5ydrclNYL-9NIbDIWJ5Y5x4UBp9cGzhAjdDCI4"
        )
        
        collection_name = "cattlytics_knowledge_base"
        
        logger.info(f"Connecting to Qdrant Cloud...")
        logger.info(f"Collection: {collection_name}")
        
        # Get collection info
        logger.info("=" * 60)
        logger.info("COLLECTION INFORMATION")
        logger.info("=" * 60)
        
        try:
            collection_info = await client.get_collection(collection_name)
            logger.info(f"✅ Collection exists: {collection_name}")
            logger.info(f"📊 Points count: {collection_info.points_count}")
            logger.info(f"📐 Vector size: {collection_info.config.params.vectors.size}")
            logger.info(f"📏 Distance metric: {collection_info.config.params.vectors.distance}")
            logger.info(f"🔧 Status: {collection_info.status}")
        except Exception as e:
            logger.error(f"❌ Error getting collection info: {e}")
            return
        
        # Get stored points
        logger.info("\n" + "=" * 60)
        logger.info("STORED VECTORS/POINTS")
        logger.info("=" * 60)
        
        try:
            # Scroll through points (get all points with payload but without vectors to save bandwidth)
            scroll_result = await client.scroll(
                collection_name=collection_name,
                limit=100,  # Get up to 100 points
                with_payload=True,
                with_vectors=False  # Don't fetch the actual vectors (too large to display)
            )
            
            points = scroll_result[0]  # First element is the list of points
            
            logger.info(f"📦 Found {len(points)} stored points:")
            
            for i, point in enumerate(points, 1):
                logger.info(f"\n🔸 Point {i}:")
                logger.info(f"   ID: {point.id}")
                
                payload = point.payload
                if payload:
                    logger.info(f"   📄 Content preview: {payload.get('content', 'N/A')[:100]}...")
                    logger.info(f"   📝 Heading: {payload.get('heading_text', 'N/A')}")
                    logger.info(f"   📊 Heading level: {payload.get('heading_level', 'N/A')}")
                    logger.info(f"   🏷️  UI features: {payload.get('ui_features', [])}")
                    logger.info(f"   📁 File: {payload.get('file_name', 'N/A')}")
                    logger.info(f"   🔢 Word count: {payload.get('word_count', 'N/A')}")
                    logger.info(f"   🤖 Embedding model: {payload.get('embedding_model', 'N/A')}")
                    logger.info(f"   ⏰ Timestamp: {payload.get('embedding_timestamp', 'N/A')}")
                else:
                    logger.info("   ⚠️  No payload data")
        
        except Exception as e:
            logger.error(f"❌ Error retrieving points: {e}")
            return
        
        # Test a simple search
        logger.info("\n" + "=" * 60)
        logger.info("SAMPLE SEARCH TEST")
        logger.info("=" * 60)
        
        try:
            # Create a dummy query vector (all zeros) just to test search functionality
            dummy_vector = [0.0] * 1536  # 1536 dimensions for text-embedding-3-small
            
            search_results = await client.search(
                collection_name=collection_name,
                query_vector=dummy_vector,
                limit=3,
                with_payload=True,
                with_vectors=False
            )
            
            logger.info(f"🔍 Search test returned {len(search_results)} results")
            for i, result in enumerate(search_results, 1):
                logger.info(f"   Result {i}: Score {result.score:.4f} - {result.payload.get('heading_text', 'N/A')}")
        
        except Exception as e:
            logger.error(f"❌ Error in search test: {e}")
        
        # Close connection
        await client.close()
        logger.success("✅ Inspection completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Failed to inspect Qdrant vectors: {e}")


async def main():
    """Main function."""
    logger.info("🔍 QDRANT CLOUD VECTOR INSPECTION")
    logger.info("=" * 60)
    
    await inspect_qdrant_vectors()


if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO"
    )
    
    # Run the inspection
    asyncio.run(main())
