"""
Simple script to parse the Cattlytics Knowledge Base Scenarios document
and examine its structure for chunking strategy.
"""

import asyncio
import os
from pathlib import Path

# Set up basic environment for testing
os.environ["OPENAI_API_KEY"] = "test-key"  # Placeholder for parsing only

from app.bots.knowledge_base_bot.config import ParsingConfig
from app.bots.knowledge_base_bot.ingestion.document_parser import DocumentParser


async def main():
    """Parse the document and examine its structure."""
    
    # Configure parser
    config = ParsingConfig(
        timeout_seconds=300,
        max_file_size_mb=100,
        supported_formats=["pdf", "docx", "pptx", "html", "htm", "md", "txt"],
        enable_ocr=True,
        extract_tables=True,
        extract_images=True
    )
    
    # Initialize parser
    parser = DocumentParser(config)
    
    # Parse the document
    document_path = "Cattlytics – Knowledge Base Scenarios.docx"
    
    if not Path(document_path).exists():
        print(f"Document not found: {document_path}")
        return
    
    print(f"Parsing document: {document_path}")
    parsed_doc = await parser.parse(document_path)
    
    if not parsed_doc:
        print("Failed to parse document")
        return
    
    print(f"\n=== Document Analysis ===")
    print(f"File: {parsed_doc.metadata['file_name']}")
    print(f"Size: {parsed_doc.metadata['file_size']} bytes")
    print(f"Word count: {parsed_doc.word_count}")
    print(f"Tables found: {len(parsed_doc.tables)}")
    print(f"Images found: {len(parsed_doc.images)}")
    
    print(f"\n=== Content Structure ===")
    content = parsed_doc.content
    
    # Analyze headings
    lines = content.split('\n')
    headings = []
    for i, line in enumerate(lines):
        line = line.strip()
        if line.startswith('#'):
            level = len(line) - len(line.lstrip('#'))
            heading_text = line.lstrip('#').strip()
            headings.append({
                'level': level,
                'text': heading_text,
                'line_number': i + 1
            })
    
    print(f"Headings found: {len(headings)}")
    for heading in headings:
        indent = "  " * (heading['level'] - 1)
        print(f"{indent}H{heading['level']}: {heading['text']} (line {heading['line_number']})")
    
    # Show content preview
    print(f"\n=== Content Preview (first 1000 chars) ===")
    print(content[:1000])
    print("..." if len(content) > 1000 else "")
    
    # Analyze content sections
    print(f"\n=== Content Sections Analysis ===")
    sections = content.split('\n\n')  # Split by double newlines
    print(f"Sections (by double newline): {len(sections)}")
    
    for i, section in enumerate(sections[:10]):  # Show first 10 sections
        section = section.strip()
        if section:
            print(f"Section {i+1}: {len(section)} chars - {section[:100]}...")
    
    # Save full content to file for examination
    output_file = "parsed_content.md"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"\nFull content saved to: {output_file}")
    
    # Analyze for cattle farming activities
    print(f"\n=== Activity Analysis ===")
    content_lower = content.lower()
    
    # Activity keywords
    bull_calf_keywords = ['bull', 'calf', 'calves', 'breeding', 'weaning', 'sire']
    cows_keywords = ['cow', 'cows', 'dairy', 'milk', 'milking', 'lactation', 'udder']
    general_keywords = ['cattle', 'livestock', 'pasture', 'feed', 'veterinary', 'health', 'farm']
    
    bull_calf_count = sum(content_lower.count(keyword) for keyword in bull_calf_keywords)
    cows_count = sum(content_lower.count(keyword) for keyword in cows_keywords)
    general_count = sum(content_lower.count(keyword) for keyword in general_keywords)
    
    print(f"Bull/Calf related terms: {bull_calf_count}")
    print(f"Cows/Dairy related terms: {cows_count}")
    print(f"General cattle terms: {general_count}")
    
    # Suggest chunking strategy
    print(f"\n=== Chunking Strategy Recommendations ===")
    
    if len(headings) > 0:
        print("✓ Document has clear heading structure")
        print("✓ Recommended: Heading-based chunking")
        print(f"  - {len(headings)} potential chunks based on headings")
        
        # Estimate chunk sizes
        if len(headings) > 1:
            avg_section_size = len(content) // len(headings)
            print(f"  - Average section size: ~{avg_section_size} characters")
            
            if avg_section_size > 2000:
                print("  - Sections may be large, consider sub-chunking within sections")
            elif avg_section_size < 500:
                print("  - Sections may be small, consider combining related sections")
    else:
        print("⚠ No clear heading structure found")
        print("✓ Recommended: Semantic sentence-based chunking")
    
    # Activity-based filtering recommendation
    total_activity_terms = bull_calf_count + cows_count + general_count
    if total_activity_terms > 10:
        print("✓ Document contains cattle farming terminology")
        print("✓ Activity-based filtering recommended")
        
        if bull_calf_count > cows_count and bull_calf_count > general_count:
            print("  - Primary focus appears to be: Bull/Calf activities")
        elif cows_count > bull_calf_count and cows_count > general_count:
            print("  - Primary focus appears to be: Cow/Dairy activities")
        else:
            print("  - Primary focus appears to be: General cattle activities")
    else:
        print("⚠ Limited cattle farming terminology detected")
        print("? Activity-based filtering may not be necessary")


if __name__ == "__main__":
    asyncio.run(main())
